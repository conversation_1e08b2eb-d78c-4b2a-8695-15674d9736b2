<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
  <title>黑马点评</title>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="./css/element.css">
  <link href="./css/main.css" rel="stylesheet">
  <link href="./css/info.css" rel="stylesheet">

  <style type="text/css">

  </style>
</head>
<body>
<div id="app">
  <div class="header">
    <div class="header-back-btn" @click="goBack" ><i class="el-icon-arrow-left"></i></div>
    <div class="header-title">资料编辑&nbsp;&nbsp;&nbsp;</div>
  </div>
  <div class="edit-container">
    <div class="info-box">
      <div class="info-item">
        <div class="info-label">头像</div>
        <div class="info-btn">
          <img width="35" :src="user.icon || '/imgs/icons/default-icon.png'" alt="">
          <div><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-item">
        <div class="info-label">昵称</div>
        <div class="info-btn">
          <div>{{user.nickName}}</div>
          <div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-item">
        <div class="info-label">个人介绍</div>
        <div class="info-btn">
          <div style="overflow: hidden; width: 150px;text-align: right">{{info.introduce || '介绍一下自己'}}</div>
          <div>
            <i class="el-icon-arrow-right"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="info-box">
      <div class="info-item">
        <div class="info-label">性别</div>
        <div class="info-btn">
          <div>{{info.gender || '选择'}}</div>
          <div><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-item">
        <div class="info-label">城市</div>
        <div class="info-btn">
          <div>{{info.city || '选择'}}</div>
          <div><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-item">
        <div class="info-label">生日</div>
        <div class="info-btn">
          <div>{{info.birthday || '添加'}}</div>
          <div><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
    </div>

    <div class="info-box">
      <div class="info-item">
        <div class="info-label">我的积分</div>
        <div class="info-btn">
          <div>查看积分</div>
          <div><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
      <div class="divider"></div>
      <div class="info-item">
        <div class="info-label">会员等级</div>
        <div class="info-btn">
          <div><a href="javascript:void(0)">成为VIP尊享特权</a></div>
          <div><i class="el-icon-arrow-right"></i></div>
        </div>
      </div>
    </div>
  </div>
  <foot-bar :active-btn="4"></foot-bar>
</div>
<script src="./js/vue.js"></script>
<script src="./js/axios.min.js"></script>
<!-- 引入组件库 -->
<script src="./js/element.js"></script>
<script src="./js/common.js"></script>
<script src="./js/footer.js"></script>
<script>

  const app = new Vue({
    el: "#app",
    data: {
      user: "",
      info: {},
    },
    created() {
      this.checkLogin();
    },
    methods: {
      checkLogin() {
        // 查询用户信息
        axios.get("/user/me")
        .then(({data}) => {
          this.user = data;
          this.info = JSON.parse(sessionStorage.getItem("userInfo")) || {}
        })
        .catch(err => {
          this.$message.error(err);
          setTimeout(() => location.href = "login.html", 1000)
        })
      },
      goBack(){
        history.back();
      }
    },

  })
</script>
</body>
</html>