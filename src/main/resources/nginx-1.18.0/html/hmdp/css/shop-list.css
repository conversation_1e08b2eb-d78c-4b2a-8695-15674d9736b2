
.header,.sort-bar,.shop-list {
    background-color: #fff;
}
#app{
    background-color: #f1f1f1;
}
.header{
    width: 100%;
    height: 7%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 2px solid #ff6633;
}
.header-back-btn{
    width: 10%;
    color: #ff6633;
    font-size: 24px;
    font-weight: bold;
}
.header-title {
    width: 80%;
    text-align: center;
    font-size: 18px;
    font-family: Hiragino Sans GB,Arial,Helvetica,"\5B8B\4F53",sans-serif;
}
.header-search {
    width: 10%;
    text-align: center;
    font-size: 18px;
    color: #ff6633;
}
.el-dropdown,.el-dropdown-menu__item {
    font-size: 12px;
    line-height: 20px;
}
.sort-bar {
    display: flex;
    justify-content: space-around;
    height: 6%;
    align-items: center;
    margin-bottom: 5px;
}
.sort-item {
    width: 20%;
    text-align: center;
    font-size: 12px;
}
.shop-list{
    height: 87%;
    background-color: #f1f1f1;
    overflow-y: auto;
}
.shop-box {
    display: flex;
    padding: 10px;
    margin-bottom: 5px;
    border-radius: 3px;
    background-color: #fff;
}
.shop-img {
    text-align: center;
    padding: 5px;
}
.shop-img img{
    width: 95px;
    height: 95px;
    border-radius: 5px;
}
.shop-info {
    width: 65%;
}
.shop-title {
    font-weight: bold;
    font-size: 14px;
}
.shop-rate {
    display: flex;
    justify-content: space-between;
}

.shop-area {
    color: #6f6f71;
    display: flex;
    justify-content: space-between;
}
.shop-item {
    line-height: 20px;
    align-items: center;
}
.shop-address{
    display: flex;
}
.shop-address i{
    color: #6c6767;
}
.shop-address span{
    height: 20px;
    line-height: 20px;
    overflow: hidden;
}