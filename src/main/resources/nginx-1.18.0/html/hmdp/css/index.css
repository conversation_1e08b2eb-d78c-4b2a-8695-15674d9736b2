/*搜索框*/
.search-bar{
    background-color: #ff6633;
    color: white;
    display: flex;
    justify-content: space-around;
    height: 7%;
    align-items: center;
    padding: 0 8px;
}
.header-icon{
    /*width: 2%;*/
    font-size: 14px;
}
.search-input{
    width: 70%;
}
.type-list {
    height: 25%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}
.type-box{
    width: 17%;
    text-align: center;
}
.type-box img {
    width: 100%;
}
.type-text{
    color: #111;
    font-size: 10px;
    margin-top: -8px;
    margin-bottom: 10px;
}

/*达人探店列表*/
.blog-list {
    background-color: #eee;
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    height: 60%;
    overflow-y: auto;
}
.blog-box{
    width: 48%;
    background-color: #fff;
    margin: 5px 0;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}
.blog-img img{
    width: 100%;
    border-radius: 3px;
}
.blog-title {
    padding: 2px 10px;
    height: 36px;
    width: 92%;
    overflow: hidden;
}
.blog-foot {
    display: flex;
    justify-content: space-between;
    margin: 10px 0 5px 0;
    padding: 0 10px;
}
.blog-user-icon {
    width: 10%;
    margin-right: 3px;
}
.blog-user-name {
    width: 65%;
    overflow: hidden;
}
.blog-user-icon img{
    width: 100%;
}
.blog-liked {
    width: 25%;
    display: flex;
    justify-content: flex-end;
}
.blog-liked img{
    width: 30%;
    height: 75%;
    margin-right: 2px;
}
