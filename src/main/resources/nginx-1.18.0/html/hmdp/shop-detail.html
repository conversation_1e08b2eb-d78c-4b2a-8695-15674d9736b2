<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
  <title>黑马点评</title>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="./css/element.css">

  <link href="./css/shop-detail.css" rel="stylesheet">
  <link href="./css/main.css" rel="stylesheet">

  <style type="text/css">

  </style>
</head>
<body>
<div id="app">
  <div class="header">
    <div class="header-back-btn" @click="goBack"><i class="el-icon-arrow-left"></i></div>
    <div class="header-title"></div>
    <div class="header-share">...</div>
  </div>
  <div class="top-bar"></div>
  <div class="shop-info-box">
    <div class="shop-title">{{shop.name}}</div>
    <div class="shop-rate">
      <el-rate
          disabled v-model="shop.score/10"
          text-color="#F63"
          show-score
      ></el-rate>
      <span>{{shop.comments}}条</span>
    </div>
    <div class="shop-rate-info"> 口味:4.9  环境:4.8  服务:4.7 </div>
    <div class="shop-rank">
      <img src="/imgs/bd.png" width="63" height="20" alt="">
      <span>拱墅区好评榜第3名</span>
      <div><i class="el-icon-arrow-right"></i></div>
    </div>
    <div class="shop-images">
      <div v-for="(s,i) in shop.images" :key="i">
        <img :src="s" alt="">
      </div>
    </div>
    <div class="shop-address">
      <div><i class="el-icon-map-location"></i></div>
      <span>{{shop.address}}</span>
      <div style="width: 10px; flex-grow: 2; text-align: center; color: #e1e2e3">|</div>
      <div style="margin: 0 5px"><img src="https://p0.meituan.net/travelcube/bf684aa196c870810655e45b1e52ce843484.png@24w_16h_40q" alt=""></div>
      <div><img src="https://p0.meituan.net/travelcube/9277ace32123e0c9f59dedf4407892221566.png@24w_24h_40q" alt=""></div>
    </div>
  </div>
  <div class="shop-divider"></div>
  <div class="shop-open-time">
    <span><i class="el-icon-watch"></i></span>
    <div>营业时间</div>
    <div>{{shop.openHours}}</div>
    <span class="line-right">查看详情 <i class="el-icon-arrow-right"></i></span>
  </div>
  <div class="shop-divider"></div>
  <div class="shop-voucher">
    <div>
      <span class="voucher-icon">券</span>
      <span style="font-weight: bold;">代金券</span>
    </div>
    <div class="voucher-box" v-for="v in vouchers" :key="v.id" v-if="!isEnd(v)">
      <div class="voucher-circle">
        <div class="voucher-b"></div>
        <div class="voucher-b"></div>
        <div class="voucher-b"></div>
      </div>
      <div class="voucher-left">
        <div class="voucher-title">{{v.title}}</div>
        <div class="voucher-subtitle">{{v.subTitle}}</div>
        <div class="voucher-price"><div>￥ {{util.formatPrice(v.payValue)}}</div>  <span>{{(v.payValue*10)/v.actualValue}}折</span></div>
      </div>
      <div class="voucher-right">
        <div v-if="v.type" class="seckill-box">
          <div class="voucher-btn" :class="{'disable-btn': isNotBegin(v) || v.stock < 1}" @click="seckill(v)">限时抢购</div>
          <div class="seckill-stock">剩余 <span>{{v.stock}}</span> 张</div>
          <div class="seckill-time">{{formatTime(v)}}</div>
        </div>
        <div class="voucher-btn" v-else>抢购</div>
      </div>
    </div>
  </div>
  <div class="shop-divider"></div>
  <div class="shop-comments">
    <div class="comments-head">
      <div>网友评价 <span>（119）</span></div>
      <div><i class="el-icon-arrow-right"></i></div>
    </div>
    <div class="comment-tags">
      <div class="tag">味道赞(19)</div>
      <div class="tag">牛肉赞(16)</div>
      <div class="tag">菜品不错(11)</div>
      <div class="tag">回头客(4)</div>
      <div class="tag">分量足(4)</div>
      <div class="tag">停车方便(3)</div>
      <div class="tag">海鲜棒(3)</div>
      <div class="tag">饮品赞(3)</div>
      <div class="tag">朋友聚餐(6)</div>
    </div>
    <div class="comment-list">
      <div class="comment-box" v-for="i in 3" :key="i">
        <div class="comment-icon">
          <img src="https://p0.meituan.net/userheadpicbackend/57e44d6eba01aad0d8d711788f30a126549507.jpg%4048w_48h_1e_1c_1l%7Cwatermark%3D0" alt="">
        </div>
        <div class="comment-info">
          <div class="comment-user">叶小乙 <span>Lv5</span></div>
          <div style="display: flex;">
            打分
            <el-rate disabled v-model="4.5" ></el-rate>
          </div>
          <div style="padding: 5px 0; font-size: 14px">
            某平台上买的券，价格可以当工作餐吃，虽然价格便宜，但是这家店一点都没有...
          </div>
          <div class="comment-images">
            <img src="https://qcloud.dpfile.com/pc/6T7MfXzx7USPIkSy7jzm40qZSmlHUF2jd-FZUL6WpjE9byagjLlrseWxnl1LcbuSGybIjx5eX6WNgCPvcASYAw.jpg" alt="">
            <img src="https://qcloud.dpfile.com/pc/sZ5q-zgglv4VXEWU71xCFjnLM_jUHq-ylq0GKivtrz3JksWQ1f7oBWZsxm1DWgcaGybIjx5eX6WNgCPvcASYAw.jpg" alt="">
            <img src="https://qcloud.dpfile.com/pc/xZy6W4NwuRFchlOi43DVLPFsx7KWWvPqifE1JTe_jreqdsBYA9CFkeSm2ZlF0OVmGybIjx5eX6WNgCPvcASYAw.jpg" alt="">
            <img src="https://qcloud.dpfile.com/pc/xZy6W4NwuRFchlOi43DVLPFsx7KWWvPqifE1JTe_jreqdsBYA9CFkeSm2ZlF0OVmGybIjx5eX6WNgCPvcASYAw.jpg" alt="">
          </div>
          <div>
            浏览641 &nbsp;&nbsp;&nbsp;&nbsp;评论5
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: space-between;padding: 15px 0; border-top: 1px solid #f1f1f1; margin-top: 10px;">
        <div>查看全部119条评价</div>
        <div><i class="el-icon-arrow-right"></i></div>
      </div>
    </div>
  </div>
  <div class="shop-divider"></div>
  <div class="copyright">
    copyright ©2021 hmdp.com
  </div>
</div>
<script src="./js/vue.js"></script>
<script src="./js/axios.min.js"></script>
<!-- 引入组件库 -->
<script src="./js/element.js"></script>
<script src="./js/common.js"></script>
<script>

  const app = new Vue({
    el: "#app",
    data: {
      util,
      shop: {},
      vouchers: []
    },
    created() {
      // 获取参数
      let shopId = util.getUrlParam("id");
      // 查询酒店信息
      this.queryShopById(shopId);
      // 查询优惠券信息
      this.queryVoucher(shopId);
    },
    methods: {
      goBack() {
        history.back();
      },
      queryShopById(shopId) {
        axios.get("/shop/" + shopId)
        .then(({data}) => {
          data.images = data.images.split(",")
          this.shop = data
        })
        .catch(this.$message.error)
      },
      queryVoucher(shopId) {
        axios.get("/voucher/list/" + shopId)
        .then(({data}) => {
          this.vouchers = data;
        })
        .catch(this.$message.error)
      },
      formatTime(v){
        let b = new Date(v.beginTime);
        let e = new Date(v.endTime);
        return b.getMonth() + 1 + "月" + b.getDate() + "日 "
          +  b.getHours() + ":" + this.formatMinutes(b.getMinutes())
          + " ~ "/*  + e.getMonth() + 1 + "月" + e.getDate() + "日 " */
          +  e.getHours() + ":" + this.formatMinutes(e.getMinutes());
      },
      formatMinutes(m){
        if(m < 10) m = "0" + m
        return m;
      },
      isNotBegin(v){
        return new Date(v.beginTime).getTime() > new Date().getTime();
      },
      isEnd(v){
        return new Date(v.endTime).getTime() < new Date().getTime();
      },
      seckill(v){
        if(!token){
          this.$message.error("请先登录")
          // 未登录，跳转
          setTimeout(() => {
            location.href = "/login.html"
          }, 200);
          return;
        }
        if(this.isNotBegin(v)){
          this.$message.error("优惠券抢购尚未开始！")
          return;
        }
        if(this.isEnd(v)){
          this.$message.error("优惠券抢购已经结束！")
          return;
        }
        if(v.stock < 1){
          this.$message.error("库存不足，请刷新再试试！")
          return;
        }
        let id = v.id;
        // 秒杀抢购
        axios.post("/voucher-order/seckill/" + id)
        .then(({data}) => {
          // 抢购成功，这里输出订单id，支付功能TODO
          this.$message.success("抢购成功，订单id：" + data)
        })
        .catch(this.$message.error)
      }
    }
  })
</script>
</body>
</html>