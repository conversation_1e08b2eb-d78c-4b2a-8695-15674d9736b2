<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
  <title>黑马点评</title>
  <!-- 引入样式 -->
  <link rel="stylesheet" href="./css/element.css">

  <link href="./css/shop-list.css" rel="stylesheet">
  <link href="./css/main.css" rel="stylesheet">

  <style type="text/css">

  </style>
</head>
<body>
<div id="app">
  <div class="header">
    <div class="header-back-btn" @click="goBack"><i class="el-icon-arrow-left"></i></div>
    <div class="header-title">{{typeName}}</div>
    <div class="header-search">
      <i class="el-icon-search"></i>
    </div>
  </div>
  <div class="sort-bar">
    <div class="sort-item">
      <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link">
        {{typeName}}<i class="el-icon-arrow-down el-icon--right"></i>
      </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="t in types" :key="t.id" :command="t">
            {{t.name}}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="sort-item" @click="sortAndQuery('')">
      距离 <i class="el-icon-arrow-down el-icon--right"></i>
    </div>
    <div class="sort-item" @click="sortAndQuery('comments')">
      人气 <i class="el-icon-arrow-down el-icon--right"></i>
    </div>
    <div class="sort-item" @click="sortAndQuery('score')">
      评分 <i class="el-icon-arrow-down el-icon--right"></i>
    </div>
  </div>
  <div class="shop-list" @scroll="onScroll">
    <div class="shop-box" v-for="s in shops" :key="s.id" @click="toDetail(s.id)">
      <div class="shop-img"><img :src="s.images" alt=""></div>
      <div class="shop-info">
        <div class="shop-title shop-item">{{s.name}}</div>
        <div class="shop-rate shop-item" >
          <el-rate
              disabled v-model="s.score/10"
              text-color="#F63"
              show-score
          ></el-rate>
          <span>{{s.comments}}条</span>
        </div>
        <div class="shop-area shop-item">
          <span>{{s.area}}</span>
          <span v-if="s.distance">{{s.distance < 1000 ? s.distance.toFixed(1) + 'm' : (s.distance/1000).toFixed(1) + 'km'}}</span>
        </div>
        <div class="shop-avg shop-item">￥{{s.avgPrice}}/人</div>
        <div class="shop-address shop-item">
          <i class="el-icon-map-location"></i>
          <span>{{s.address}}</span>
        </div>
      </div>
    </div>
  </div>
</div>
<script src="./js/vue.js"></script>
<script src="./js/axios.min.js"></script>
<!-- 引入组件库 -->
<script src="./js/element.js"></script>
<script src="./js/common.js"></script>
<script>

  const app = new Vue({
    el: "#app",
    data: {
      util,
      isReachBottom: false,
      types: [], // 类型列表
      shops: [], // 商店列表
      typeName: "",
      params:{
        typeId: 0,
        current: 1,
        sortBy: "",
        x: 120.149993, // 经度
        y: 30.334229 // 纬度
      }
    },
    created() {
      // 获取参数
      this.params.typeId = util.getUrlParam("type");
      this.typeName = util.getUrlParam("name");
      // 查询类型
      this.queryTypes();
      // 查询商店
      this.queryShops();
    },
    methods: {
      queryTypes() {
        axios.get("/shop-type/list")
          .then(({data}) => {
            this.types = data;
          })
          .catch(err => {
            console.log(err);
            this.$message.error(err)
          })
      },
      queryShops() {
        axios.get("/shop/of/type", {
            params: this.params
          })
          .then(({data}) => {
            if (!data) {
              return
            }
            data.forEach(s => s.images = s.images.split(',')[0]);
            this.shops = this.shops.concat(data);
          })
          .catch(err => {
            console.log(err);
            this.$message.error(err)
          })
      },
      handleCommand(t) {
        location.href = "/shop-list.html?type="+t.id+"&name="+t.name;
      },
      sortAndQuery(sortBy) {
        this.params.sortBy = sortBy;
        this.queryShops();
      },
      goBack() {
        history.back();
      },
      toDetail(id) {
        location.href = "/shop-detail.html?id="+id
      },
      onScroll(e) {
        let scrollTop = e.target.scrollTop;
        let offsetHeight = e.target.offsetHeight;
        let scrollHeight = e.target.scrollHeight;
        if(scrollTop + offsetHeight + 1 > scrollHeight && !this.isReachBottom){
          this.isReachBottom = true
          console.log("触底")
          this.params.current++
          this.queryShops(this.params.current, 5);
        }else{
          this.isReachBottom = false
        }
      },
    }
  })
</script>
</body>
</html>